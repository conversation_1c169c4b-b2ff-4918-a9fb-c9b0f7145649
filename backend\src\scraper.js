
import {chromium} from 'playwright';

(async () => {
const browser = await chromium.launch({ headless: true });
const context = await browser.newContext();
const page = await context.newPage();

await page.goto('https://news.ycombinator.com', { waitUntil: 'domcontentloaded' });

await page.waitForSelector('.athing');

const news = await page.$$eval('.athing', news =>
news.slice(0,30).map((n) => {
    const rank = n.querySelector('.rank')?.innerText.slice(0, -1);
    const tittle = n.querySelector('.titleline a')?.textContent;

    const sub = n.nextElementSibling?.querySelector('.subtext');
    const pointsText = sub?.querySelector('.score')?.innerText ?? 'No points information';
    const points = parseInt(pointsText, 10) || 0;
    const commentsText= sub?.querySelector('a[href^="item?id="]').innerText ?? 'No comments information';
    const comments = commentsText.includes('comments')? parseInt(commentsText, 10) : 0;

    return {rank, tittle, points, comments };
    
})
   
//    console.log(news);
//    await browser.close();
}

})();